//
//  HomeCoordinator.swift
//  Home
//
//  Created by <PERSON> on 19/02/2024.
//

import XCoordinator
import SharedData
import RxSwift
import CUIModule

public enum HomeRoute: Route {
    case home
    case watchListSorting(sorting: WatchListSortingType,
                          onApply: AnyObserver<WatchListSortingType>)
    case newsList
    case newsDetail(url: String)
    case appSettings
    case search
    case notification
    case messageCenter
    case myOrders(status: OrderStatus?)
    case activeAutoOrders
    case queuedOrders
    case instrumentDetail(instrument: MarketInstrument)
    case walletAction(action: WalletAction)
    case statementRequest
    case favoriteList
    case onboarding(UserAccountType)
    case fundRecommendationList(fullCategory: FundRecommendationCategory, mainCategoryName: String, selectedCategoryIndex: Int)

    case webview(title: String, urlString: String)
}

public final class HomeCoordinator: NavigationCoordinator<HomeRoute> {
    
    private let openSettings: (() -> Void)
    public var openSubRoute: ((CommonSubRoute) -> Void)?
    private let onboarding: ((_ accountType: UserAccountType) -> Void)
    
    public init(openSettings: @escaping (() -> Void),
                onboarding: @escaping ((_ accountType: UserAccountType) -> Void)) {
        self.openSettings = openSettings
        self.onboarding = onboarding
        
        super.init(initialRoute: .home)
    }
    
    public override func prepareTransition(for route: HomeRoute) -> NavigationTransition {
        switch route {
        case .home:
            return .push(createHome())
            
        case .watchListSorting(let sorting, let onApply):
            let viewController = WatchListSortingViewController(currentSorting: sorting,
                                                                onApply: onApply)
            viewController.hidesBottomBarWhenPushed = true
            
            return .push(viewController)
            
        case .newsList:
            let viewController = NewsListViewController()
            let viewModel = NewsListViewModel(router: unownedRouter)
            viewController.bind(viewModel: viewModel)
            
            return .push(viewController)
            
        case .newsDetail(let url):
            let viewController = NewsDetailViewController(urlString: url)
            viewController.hidesBottomBarWhenPushed = true
            
            return .push(viewController)

        case .appSettings:
            self.openSettings()
            return .none()
            
        case .search:
            openSubRoute?(.search(navigation: self.rootViewController))
            return .none()
            
        case .notification:
            openSubRoute?(.notification(navigation: self.rootViewController))
            return .none()

        case .messageCenter:
            openSubRoute?(.messageCenter(navigation: self.rootViewController))
            return .none()
            
        case .myOrders(let status):
            openSubRoute?(.historyOrder(status: status))
            return .none()
            
        case .activeAutoOrders:
            openSubRoute?(.activeAutoOrders)
            return .none()
            
        case .queuedOrders:
            openSubRoute?(.queuedOrders)
            return .none()
            
        case .instrumentDetail(let instrument):
            openSubRoute?(.marketDashboard(navigation: rootViewController,
                                           data: instrument))
            
            return .none()
            
        case .walletAction(let action):
            openSubRoute?(.walletAction(action))
            
            return .none()
            
        case .statementRequest:
            openSubRoute?(.statementRequest)
            
            return .none()
            
        case .favoriteList:
            openSubRoute?(.favoriteList)
            
            return .none()
            
        case .onboarding(let type):
            self.onboarding(type)
            return .none()
            
        case .fundRecommendationList(let fullCategory, let mainCategoryName, let selectedCategoryIndex):
            let viewController = FundRecommendationListViewController()
            let viewModel = FundRecommendationListViewModel(
                router: unownedRouter,
                fullCategory: fullCategory,
                mainCategoryName: mainCategoryName,
                selectedCategoryIndex: selectedCategoryIndex
            )
            viewController.bind(viewModel: viewModel)
            viewController.hidesBottomBarWhenPushed = true

            return .push(viewController)

        case .webview(let title, let urlString):
            let viewController = SimpleWebViewController(titleString: title, urlString: urlString)
            viewController.hidesBottomBarWhenPushed = true

            return .push(viewController)
        }
    }
    
    private func createHome() -> HomeViewController {
        let viewController = HomeViewController()
        let viewModel = HomeViewModel(router: unownedRouter)
        viewController.bind(viewModel: viewModel)
        
        return viewController
    }
}

// MARK: - Custom Theme
extension HomeCoordinator {

    private enum ResetOnThemeChanged {
        case home(Int)
    }

    public func themeChanged() {

        var viewControllers = rootViewController.viewControllers

        viewsNeedReset().forEach {
            switch $0 {
            case .home(let index):
                viewControllers[index] = createHome()
            }
        }
        rootViewController.setViewControllers(viewControllers, animated: false)
    }

    private func viewsNeedReset() -> [ResetOnThemeChanged] {
        var toReset: [ResetOnThemeChanged] = []
        rootViewController.viewControllers.enumerated().forEach { index, value in
            switch value.className {
            case HomeViewController.className:
                toReset.append(.home(index))
            default: break
            }
        }
        return toReset
    }
}
