//
//  HomeLayoutConfig.swift
//  Home
//
//  Created by <PERSON> on 19/02/2024.
//

import UIKit
import CUIModule

// swiftlint:disable line_length
enum HomeSection: Int, CaseIterable {
    case userProfile
    case shortcut
    case message
    case watchlistHeader
    case watchList
    case banner
    case newsHeader
    case news
    case fundRecommendationHeader
    case fundRecommendation
    case meritReportHeader
    case meritReport
    
    var section: NSCollectionLayoutSection {
        switch self {
            // MARK: User profile
        case .userProfile:
            // Item
            let itemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                  heightDimension: .estimated(66))
            let item = NSCollectionLayoutItem(layoutSize: itemSize)
            
            // Group
            let groupSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                   heightDimension: .estimated(66))
            let group = NSCollectionLayoutGroup.horizontal(layoutSize: groupSize,
                                                           subitems: [item])
            
            // Section
            let section = NSCollectionLayoutSection(group: group)
            section.contentInsets = NSDirectionalEdgeInsets(top: 0,
                                                            leading: 12,
                                                            bottom: 8,
                                                            trailing: 12)
            
            return section
            
            // MARK: Shortcut
        case .shortcut:
            // Item
            let itemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                  heightDimension: .fractionalHeight(1))
            let item = NSCollectionLayoutItem(layoutSize: itemSize)
            
            // Group
            let groupSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1/4),
                                                   heightDimension: .absolute(66))
            let group = NSCollectionLayoutGroup.horizontal(layoutSize: groupSize,
                                                           subitems: [item])
            group.contentInsets = NSDirectionalEdgeInsets(top: 0, 
                                                          leading: 6,
                                                          bottom: 0,
                                                          trailing: 6)
            
            // Section
            let section = NSCollectionLayoutSection(group: group)
            section.orthogonalScrollingBehavior = .continuous
            
            return section
            
            // MARK: Message
        case .message:
            // Item
            let itemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                  heightDimension: .estimated(66))
            let item = NSCollectionLayoutItem(layoutSize: itemSize)
            
            // Group
            let groupSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                   heightDimension: .estimated(66))
            let group = NSCollectionLayoutGroup.horizontal(layoutSize: groupSize,
                                                           subitems: [item])

            // Section
            let section = NSCollectionLayoutSection(group: group)
            section.contentInsets = NSDirectionalEdgeInsets(top: 0,
                                                            leading: 12,
                                                            bottom: 0,
                                                            trailing: 12)
            section.interGroupSpacing = 8

            // Supplementary Item
            let footerItemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                        heightDimension: .estimated(26))
            let footerItem = NSCollectionLayoutBoundarySupplementaryItem(layoutSize: footerItemSize,
                                                                         elementKind: UICollectionView.elementKindSectionFooter,
                                                                         alignment: .bottom)
            section.boundarySupplementaryItems = [footerItem]
            
            return section
            
            // MARK: Watch list header
        case .watchlistHeader:
            // Item
            let itemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                  heightDimension: .fractionalHeight(1))
            let item = NSCollectionLayoutItem(layoutSize: itemSize)
            
            // Group
            let groupSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(300/375),
                                                   heightDimension: .estimated(100))
            let group = NSCollectionLayoutGroup.vertical(layoutSize: groupSize,
                                                         subitems: [item])
            // Section
            let section = NSCollectionLayoutSection(group: group)
            section.contentInsets = NSDirectionalEdgeInsets(top: 0,
                                                            leading: 12,
                                                            bottom: 0,
                                                            trailing: 12)
            // Supplementary Item
            let headerItemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                        heightDimension: .estimated(28))
            let headerItem = NSCollectionLayoutBoundarySupplementaryItem(layoutSize: headerItemSize,
                                                                         elementKind: UICollectionView.elementKindSectionHeader,
                                                                         alignment: .top)
            section.boundarySupplementaryItems = [headerItem]
            
            return section
            
            // MARK: Watch list
        case .watchList:
            // Item
            let itemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                  heightDimension: .estimated(334))
            let item = NSCollectionLayoutItem(layoutSize: itemSize)
            
            let innerGroupSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                        heightDimension: .estimated(334))
            let innerGroup = NSCollectionLayoutGroup.vertical(layoutSize: innerGroupSize,
                                                              subitems: [item])

            // Group
            let groupSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                   heightDimension: .estimated(100))
            let group = NSCollectionLayoutGroup.horizontal(layoutSize: groupSize,
                                                           subitems: [innerGroup])

            // Section
            let section = NSCollectionLayoutSection(group: group)
            section.orthogonalScrollingBehavior = .groupPagingCentered
            section.contentInsets = NSDirectionalEdgeInsets(top: 0,
                                                            leading: 12,
                                                            bottom: 8,
                                                            trailing: 12)      
            section.interGroupSpacing = 8

            return section
            
            // MARK: Banner
        case .banner:
            // Item
            let itemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                  heightDimension: .fractionalHeight(1))
            let item = NSCollectionLayoutItem(layoutSize: itemSize)
            
            // Group
            let groupSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                   heightDimension: .fractionalWidth(120/351))
            let group = NSCollectionLayoutGroup.horizontal(layoutSize: groupSize,
                                                           subitems: [item])
            group.contentInsets = NSDirectionalEdgeInsets(top: 0,
                                                          leading: 12,
                                                          bottom: 0,
                                                          trailing: 12)
            // Section
            let section = NSCollectionLayoutSection(group: group)
            section.orthogonalScrollingBehavior = .groupPagingCentered
            section.contentInsets = NSDirectionalEdgeInsets(top: 0,
                                                            leading: 8,
                                                            bottom: 8,
                                                            trailing: 8)
            
            return section
            
            // MARK: News header
        case .newsHeader:
            // Small item
            let smallItemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                       heightDimension: .estimated(60))
            let smallItem = NSCollectionLayoutItem(layoutSize: smallItemSize)
            
            let outerGroupSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                        heightDimension: .estimated(60))
            let outerGroup = NSCollectionLayoutGroup.vertical(layoutSize: outerGroupSize,
                                                              subitems: [smallItem])
            
            // Section
            let section = NSCollectionLayoutSection(group: outerGroup)
            section.contentInsets = NSDirectionalEdgeInsets(top: 0,
                                                            leading: 12,
                                                            bottom: 0,
                                                            trailing: 12)
            // Supplementary Item
            let headerItemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                        heightDimension: .estimated(12))
            let headerItem = NSCollectionLayoutBoundarySupplementaryItem(layoutSize: headerItemSize,
                                                                         elementKind: UICollectionView.elementKindSectionHeader,
                                                                         alignment: .top)
            
            section.boundarySupplementaryItems = [headerItem]
            
            return section
            
            // MARK: News
        case .news:
            // Item
            let itemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                  heightDimension: .estimated(60))
            let item = NSCollectionLayoutItem(layoutSize: itemSize)
            
            let groupSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                   heightDimension: .estimated(60))
            let group = NSCollectionLayoutGroup.vertical(layoutSize: groupSize,
                                                         subitems: [item])
            group.contentInsets = NSDirectionalEdgeInsets(top: 0,
                                                          leading: 8,
                                                          bottom: 0,
                                                          trailing: 8)
            group.interItemSpacing = .fixed(8)
            
            // Section
            let section = NSCollectionLayoutSection(group: group)
            section.contentInsets = NSDirectionalEdgeInsets(top: 8,
                                                            leading: 12,
                                                            bottom: 4,
                                                            trailing: 12)
            
            section.decorationItems = [
                NSCollectionLayoutDecorationItem.background(elementKind: RoundedBackgroundView.reuseIdentifier)
            ]
            
            // Supplementary Item
            let footerItemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                        heightDimension: .estimated(26))
            let footerItem = NSCollectionLayoutBoundarySupplementaryItem(layoutSize: footerItemSize,
                                                                         elementKind: UICollectionView.elementKindSectionFooter,
                                                                         alignment: .bottom)
            
            section.boundarySupplementaryItems = [footerItem]

            return section

            // MARK: Fund Recommendation header
        case .fundRecommendationHeader:
            // Small item
            let smallItemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                       heightDimension: .estimated(60))
            let smallItem = NSCollectionLayoutItem(layoutSize: smallItemSize)

            let outerGroupSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                        heightDimension: .estimated(60))
            let outerGroup = NSCollectionLayoutGroup.vertical(layoutSize: outerGroupSize,
                                                              subitems: [smallItem])

            // Section
            let section = NSCollectionLayoutSection(group: outerGroup)
            section.contentInsets = NSDirectionalEdgeInsets(top: 0,
                                                            leading: 12,
                                                            bottom: 0,
                                                            trailing: 12)
            // Supplementary Item
            let headerItemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                        heightDimension: .estimated(12))
            let headerItem = NSCollectionLayoutBoundarySupplementaryItem(layoutSize: headerItemSize,
                                                                         elementKind: UICollectionView.elementKindSectionHeader,
                                                                         alignment: .top)

            section.boundarySupplementaryItems = [headerItem]

            return section

            // MARK: Fund Recommendation
        case .fundRecommendation:
            // Item
            let itemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                  heightDimension: .estimated(300))
            let item = NSCollectionLayoutItem(layoutSize: itemSize)

            let groupSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                   heightDimension: .estimated(300))
            let group = NSCollectionLayoutGroup.vertical(layoutSize: groupSize,
                                                         subitems: [item])

            // Section
            let section = NSCollectionLayoutSection(group: group)
            section.contentInsets = NSDirectionalEdgeInsets(top: 8,
                                                            leading: 12,
                                                            bottom: 4,
                                                            trailing: 12)

            section.decorationItems = [
                NSCollectionLayoutDecorationItem.background(elementKind: RoundedBackgroundView.reuseIdentifier)
            ]

            return section

            // MARK: Merit Report header
        case .meritReportHeader:
            // Small item
            let smallItemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                       heightDimension: .estimated(60))
            let smallItem = NSCollectionLayoutItem(layoutSize: smallItemSize)

            let outerGroupSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                        heightDimension: .estimated(60))
            let outerGroup = NSCollectionLayoutGroup.vertical(layoutSize: outerGroupSize,
                                                              subitems: [smallItem])

            // Section
            let section = NSCollectionLayoutSection(group: outerGroup)
            section.contentInsets = NSDirectionalEdgeInsets(top: 0,
                                                            leading: 12,
                                                            bottom: 0,
                                                            trailing: 12)
            // Supplementary Item
            let headerItemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                        heightDimension: .estimated(12))
            let headerItem = NSCollectionLayoutBoundarySupplementaryItem(layoutSize: headerItemSize,
                                                                         elementKind: UICollectionView.elementKindSectionHeader,
                                                                         alignment: .top)

            section.boundarySupplementaryItems = [headerItem]

            return section

            // MARK: Merit Report
        case .meritReport:
            // Item
            let itemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                  heightDimension: .estimated(60))
            let item = NSCollectionLayoutItem(layoutSize: itemSize)

            let groupSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1),
                                                   heightDimension: .estimated(60))
            let group = NSCollectionLayoutGroup.vertical(layoutSize: groupSize,
                                                         subitems: [item])
            group.contentInsets = NSDirectionalEdgeInsets(top: 0,
                                                          leading: 8,
                                                          bottom: 0,
                                                          trailing: 8)
            group.interItemSpacing = .fixed(8)

            // Section
            let section = NSCollectionLayoutSection(group: group)
            section.contentInsets = NSDirectionalEdgeInsets(top: 8,
                                                            leading: 12,
                                                            bottom: 4,
                                                            trailing: 12)

            section.decorationItems = [
                NSCollectionLayoutDecorationItem.background(elementKind: RoundedBackgroundView.reuseIdentifier)
            ]

            return section
        }
    }
}

// MARK: - HomeViewController
extension HomeViewController {
    
    func collectionViewLayout() -> UICollectionViewLayout {
        let config = UICollectionViewCompositionalLayoutConfiguration()
        config.interSectionSpacing = 8
        
        let layout = UICollectionViewCompositionalLayout(sectionProvider: { [unowned self] sectionIndex, _ in
            let homeSection = viewModel.homeSections[sectionIndex]
            let section = homeSection.section
            
            switch homeSection {
            case .banner:
                section.visibleItemsInvalidationHandler = { [unowned self] visibleItems, scrollOffset, environment in
                    let index = Int(scrollOffset.x / UIScreen.main.bounds.width)
                    
                    bannerIndicator?.updateCurrentStep(index + 1)
                }
                
            default:
                break
            }
            
            return section
        }, configuration: config)
        
        layout.register(RoundedBackgroundView.self,
                        forDecorationViewOfKind: RoundedBackgroundView.reuseIdentifier)
        
        return layout
    }
}
// swiftlint:enable line_length
