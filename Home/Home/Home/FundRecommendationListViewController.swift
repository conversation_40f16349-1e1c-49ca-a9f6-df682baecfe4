//
//  FundRecommendationListViewController.swift
//  Home
//
//  Created by Augment Agent on 25/08/2025.
//

import CUIModule
import Core
import RxCocoa
import RxSwift
import SharedData
import UIKit

final class FundRecommendationListViewController: BaseViewController, VMView {

    // MARK: UI Properties
    private lazy var navView: CUINavigationBar = {
        let view = CUINavigationBar(
            displayModel: CUINavigationBar.DisplayModel(
                leftIcon: .image(named: "merit_ic_back"),
                titleView: titleLabel
            )
        )
        return view
    }()

    private lazy var titleLabel = UILabel(
        font: Font.semiBold.of(size: 14),
        textColor: Color.txtTitle
    )

    private lazy var categoryButtonsStackView = {
        let stackView = UIStackView(axis: .horizontal, distribution: .fillEqually, spacing: 8)
        return stackView
    }()

    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 0
        layout.sectionInset = UIEdgeInsets(top: 16, left: 16, bottom: 16, right: 16)

        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = Color.bgDefault
        collectionView.showsVerticalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self

        // Register cells
        collectionView.register(
            FundItemCollectionViewCell.self,
            forCellWithReuseIdentifier: FundItemCollectionViewCell.reuseIdentifier)

        return collectionView
    }()

    // MARK: Properties
    public var viewModel: FundRecommendationListViewModel!
    private var instruments: [FundRecommendationInstrument] = []
    private var categories: [FundRecommendationChild] = []
    private var categoryButtons: [UIButton] = []
    private var selectedCategoryIndex = 0

    // MARK: - Reactive Properties
    private let categorySelectionSubject = PublishSubject<Int>()

    // MARK: Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindViewModel()
    }

    // MARK: Setup
    public func setupUI() {
        view.backgroundColor = Color.bgDefault
        navigationBarHidden(true)

        view.addSubviews([navView, categoryButtonsStackView, collectionView])

        navView.snp.makeConstraints { make in
            make.top.left.right.equalTo(view.safeAreaLayoutGuide)
            make.height.equalTo(44)
        }

        categoryButtonsStackView.snp.makeConstraints { make in
            make.top.equalTo(navView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(32)
        }

        collectionView.snp.makeConstraints { make in
            make.top.equalTo(categoryButtonsStackView.snp.bottom).offset(16)
            make.left.right.bottom.equalToSuperview()
        }
    }

    private func bindViewModel() {
        // Handle back navigation directly in the view controller
        navView.rx.leftTap
            .subscribe(onNext: { [weak self] in
                self?.navigationController?.popViewController(animated: true)
            })
            .disposed(by: disposeBag)

        let input = FundRecommendationListViewModel.Input(
            onViewAppear: rx.viewWillAppear.mapToVoid(),
            categorySelection: categorySelectionSubject.asObservable()
        )

        let output = viewModel.transform(input: input)

        output.title
            .drive(titleLabel.rx.text)
            .disposed(by: disposeBag)

        output.categories
            .drive(onNext: { [weak self] categories in
                self?.categories = categories
                self?.setupCategoryButtons()
            })
            .disposed(by: disposeBag)

        output.selectedCategoryIndex
            .drive(onNext: { [weak self] selectedIndex in
                self?.selectedCategoryIndex = selectedIndex
                self?.selectCategoryButton(at: selectedIndex)
            })
            .disposed(by: disposeBag)

        output.instruments
            .drive(onNext: { [weak self] instruments in
                self?.instruments = instruments
                self?.collectionView.reloadData()
            })
            .disposed(by: disposeBag)
    }

    public func bind(viewModel: FundRecommendationListViewModel) {
        self.viewModel = viewModel
    }

    // MARK: - Category Button Setup
    private func setupCategoryButtons() {
        // Clear existing buttons
        categoryButtons.forEach { $0.removeFromSuperview() }
        categoryButtons.removeAll()
        categoryButtonsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // Create buttons for categories
        for (index, category) in categories.enumerated() {
            let button = createCategoryButton(title: category.categoryName, index: index)
            categoryButtons.append(button)
            categoryButtonsStackView.addArrangedSubview(button)
        }

        // Select the current button
        if !categoryButtons.isEmpty {
            selectCategoryButton(at: selectedCategoryIndex)
        }
    }

    private func createCategoryButton(title: String, index: Int) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = Font.medium.of(size: 14)
        button.layer.cornerRadius = 8
        button.layer.borderWidth = 1
        button.contentEdgeInsets = UIEdgeInsets(top: 8, left: 12, bottom: 8, right: 12)

        // Set initial appearance
        updateButtonAppearance(button, isSelected: index == selectedCategoryIndex)

        button.rx.tap
            .subscribe(onNext: { [weak self] in
                self?.selectCategoryButton(at: index)
                self?.categorySelectionSubject.onNext(index)
            })
            .disposed(by: disposeBag)

        return button
    }

    private func selectCategoryButton(at index: Int) {
        guard index < categoryButtons.count else { return }

        selectedCategoryIndex = index

        // Update button appearances
        for (buttonIndex, button) in categoryButtons.enumerated() {
            updateButtonAppearance(button, isSelected: buttonIndex == index)
        }
    }

    private func updateButtonAppearance(_ button: UIButton, isSelected: Bool) {
        if isSelected {
            button.backgroundColor = Color.btnPrimary
            button.setTitleColor(.white, for: .normal)
            button.layer.borderColor = Color.btnPrimary.cgColor
        } else {
            button.backgroundColor = .clear
            button.setTitleColor(Color.txtTitle, for: .normal)
            button.layer.borderColor = Color.borderInputDefault.cgColor
        }
    }
}

// MARK: - UICollectionViewDataSource
extension FundRecommendationListViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int)
        -> Int
    {
        return instruments.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath)
        -> UICollectionViewCell
    {
        let cell =
            collectionView.dequeueReusableCell(
                withReuseIdentifier: FundItemCollectionViewCell.reuseIdentifier,
                for: indexPath) as! FundItemCollectionViewCell
        let instrument = instruments[indexPath.item]
        cell.configure(with: instrument)
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension FundRecommendationListViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        collectionView.deselectItem(at: indexPath, animated: true)

        guard indexPath.item < instruments.count else { return }
        let instrument = instruments[indexPath.item]

        viewModel.navigateToInstrumentDetail(instrument)
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension FundRecommendationListViewController: UICollectionViewDelegateFlowLayout {
    func collectionView(
        _ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout,
        sizeForItemAt indexPath: IndexPath
    ) -> CGSize {
        let width = collectionView.frame.width - 32  // Account for section insets
        return CGSize(width: width, height: 80)  // Same height as FundItemView
    }
}

// MARK: - FundItemCollectionViewCell
private class FundItemCollectionViewCell: UICollectionViewCell, AnyView {

    private lazy var containerView = {
        let view = UIView()
        view.backgroundColor = Color.bgDefault
        view.layer.cornerRadius = 8
        return view
    }()

    private lazy var contentStackView = {
        let stackView = UIStackView(axis: .vertical, spacing: 8)
        stackView.addArrangedSubviews([topRowStackView, bottomRowStackView])
        return stackView
    }()

    private lazy var topRowStackView = {
        let stackView = UIStackView(axis: .horizontal)
        stackView.addArrangedSubviews([instrumentNameLabel, UIView(), priceChangeRateLabel])
        return stackView
    }()

    private lazy var bottomRowStackView = {
        let stackView = UIStackView(axis: .horizontal)
        stackView.addArrangedSubviews([leftBottomStackView, UIView(), periodLabel])
        return stackView
    }()

    private lazy var leftBottomStackView = {
        let stackView = UIStackView(axis: .horizontal, spacing: 8)
        stackView.addArrangedSubviews([allocationClassLabel, riskLevelBadge])
        return stackView
    }()

    private lazy var instrumentNameLabel = {
        let label = UILabel(font: Font.bold.of(size: 14), textColor: Color.txtTitle)
        label.lineBreakMode = .byTruncatingTail
        label.setContentHuggingPriority(.defaultLow, for: .horizontal)
        label.setContentCompressionResistancePriority(.defaultLow, for: .horizontal)
        return label
    }()

    private lazy var priceChangeRateLabel = {
        let label = UILabel(font: Font.semiBold.of(size: 14), textAlignment: .right)
        label.setContentHuggingPriority(.required, for: .horizontal)
        label.setContentCompressionResistancePriority(.required, for: .horizontal)
        return label
    }()
    private lazy var allocationClassLabel = UILabel(
        font: Font.regular.of(size: 12), textColor: Color.txtParagraph)
    private lazy var riskLevelBadge = RiskLevelBadge()
    private lazy var periodLabel = UILabel(
        font: Font.regular.of(size: 12), textColor: Color.txtParagraph, textAlignment: .right)

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    public func setupUI() {
        contentView.addSubview(containerView)
        containerView.addSubview(contentStackView)

        containerView.snp.makeConstraints {
            $0.edges.equalToSuperview()
        }

        contentStackView.snp.makeConstraints {
            $0.edges.equalToSuperview().inset(12)
        }
    }

    func configure(with instrument: FundRecommendationInstrument) {
        instrumentNameLabel.text = instrument.instrumentName

        // Configure price change rate with color and format to 2 decimal places
        if let priceChangeRate = instrument.priceChangeRate,
            let rateValue = Double(priceChangeRate)
        {
            let formattedRate = String(format: "%.2f", rateValue)
            let percentage = "\(formattedRate)%"

            if rateValue < 0 {
                priceChangeRateLabel.text = percentage
                priceChangeRateLabel.textColor = Color.txtNegative
            } else if rateValue == 0 {
                priceChangeRateLabel.text = percentage
                priceChangeRateLabel.textColor = Color.txtGray
            } else {
                priceChangeRateLabel.text = "+\(percentage)"
                priceChangeRateLabel.textColor = Color.txtPositive
            }
        } else {
            priceChangeRateLabel.text = "0.00%"
            priceChangeRateLabel.textColor = Color.txtGray
        }

        // Configure allocation class with proper capitalization (first letter uppercase)
        if let allocationClass = instrument.allocationClass {
            allocationClassLabel.text = allocationClass.capitalized
        } else {
            allocationClassLabel.text = ""
        }

        // Configure risk level with standard app styling
        riskLevelBadge.updateRiskLevel(RiskLevel(rawValue: instrument.riskLevel ?? ""))

        periodLabel.text = instrument.period ?? ""
    }
}
